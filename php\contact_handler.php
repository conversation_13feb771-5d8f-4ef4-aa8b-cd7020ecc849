<?php
/**
 * Contact Form Handler
 * Processes contact form submissions and stores them in the database
 */

require_once 'config.php';

// Set headers for AJAX requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Only POST requests are allowed');
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        jsonResponse(false, 'Database connection failed');
    }
    
    // Validate and sanitize input data
    $firstName = sanitizeInput($_POST['first_name'] ?? '');
    $lastName = sanitizeInput($_POST['last_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    $newsletter = isset($_POST['newsletter']) ? 1 : 0;
    $privacy = isset($_POST['privacy']) ? 1 : 0;
    
    // Validation
    $errors = [];
    
    if (empty($firstName) || strlen($firstName) < 2) {
        $errors[] = 'First name must be at least 2 characters long';
    }
    
    if (empty($lastName) || strlen($lastName) < 2) {
        $errors[] = 'Last name must be at least 2 characters long';
    }
    
    if (empty($email) || !validateEmail($email)) {
        $errors[] = 'Please provide a valid email address';
    }
    
    if (empty($subject)) {
        $errors[] = 'Please select a subject';
    }
    
    if (empty($message) || strlen($message) < 10) {
        $errors[] = 'Message must be at least 10 characters long';
    }
    
    if (!$privacy) {
        $errors[] = 'You must agree to the privacy policy';
    }
    
    // Phone validation (optional field)
    if (!empty($phone) && strlen($phone) < 10) {
        $errors[] = 'Please provide a valid phone number';
    }
    
    if (!empty($errors)) {
        jsonResponse(false, implode('. ', $errors));
    }
    
    // Check for duplicate submissions (same email and message in last 5 minutes)
    $stmt = $conn->prepare("
        SELECT id FROM contacts 
        WHERE email = ? AND message = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ");
    $stmt->execute([$email, $message]);
    
    if ($stmt->rowCount() > 0) {
        jsonResponse(false, 'Duplicate submission detected. Please wait before submitting again.');
    }
    
    // Insert contact into database
    $stmt = $conn->prepare("
        INSERT INTO contacts (first_name, last_name, email, phone, subject, message, newsletter) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $firstName,
        $lastName,
        $email,
        $phone,
        $subject,
        $message,
        $newsletter
    ]);
    
    if (!$result) {
        jsonResponse(false, 'Failed to save your message. Please try again.');
    }
    
    $contactId = $conn->lastInsertId();
    
    // Handle newsletter subscription
    if ($newsletter) {
        subscribeToNewsletter($conn, $email);
    }
    
    // Send confirmation email to user
    $userEmailSent = sendUserConfirmation($firstName, $email, $subject);
    
    // Send notification email to admin
    $adminEmailSent = sendAdminNotification($firstName, $lastName, $email, $subject, $message, $phone);
    
    // Prepare response
    $responseMessage = 'Thank you for your message! We will get back to you within 24 hours.';
    
    if (!$userEmailSent) {
        $responseMessage .= ' Note: Confirmation email could not be sent.';
    }
    
    jsonResponse(true, $responseMessage, [
        'contact_id' => $contactId,
        'confirmation_sent' => $userEmailSent,
        'admin_notified' => $adminEmailSent
    ]);
    
} catch (Exception $e) {
    error_log("Contact form error: " . $e->getMessage());
    jsonResponse(false, 'An error occurred while processing your request. Please try again.');
}

/**
 * Subscribe user to newsletter
 */
function subscribeToNewsletter($conn, $email) {
    try {
        $stmt = $conn->prepare("
            INSERT IGNORE INTO newsletter_subscribers (email) VALUES (?)
        ");
        return $stmt->execute([$email]);
    } catch (Exception $e) {
        error_log("Newsletter subscription error: " . $e->getMessage());
        return false;
    }
}

/**
 * Send confirmation email to user
 */
function sendUserConfirmation($firstName, $email, $subject) {
    $emailSubject = "Thank you for contacting " . SITE_NAME;
    
    $emailBody = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(45deg, #8B4513, #FFD700); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #FFF5E1; }
            .footer { padding: 15px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Sweet Delights Baking Institution</h1>
            </div>
            <div class='content'>
                <h2>Thank you for contacting us!</h2>
                <p>Dear {$firstName},</p>
                <p>We have received your message regarding: <strong>{$subject}</strong></p>
                <p>Our team will review your inquiry and get back to you within 24 hours during business hours.</p>
                <p>If you have any urgent questions, please call us at +****************.</p>
                <p>Best regards,<br>The Sweet Delights Team</p>
            </div>
            <div class='footer'>
                <p>Sweet Delights Baking Institution<br>
                123 Baking Street, Sweet City<br>
                Phone: +**************** | Email: <EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    $headers = "From: " . SITE_NAME . " <" . SITE_EMAIL . ">\r\n";
    $headers .= "Reply-To: " . SITE_EMAIL . "\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return sendEmail($email, $emailSubject, $emailBody, $headers);
}

/**
 * Send notification email to admin
 */
function sendAdminNotification($firstName, $lastName, $email, $subject, $message, $phone) {
    $emailSubject = "New Contact Form Submission - " . SITE_NAME;
    
    $emailBody = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #8B4513; color: white; padding: 15px; }
            .content { padding: 20px; background: #f9f9f9; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #8B4513; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>New Contact Form Submission</h2>
            </div>
            <div class='content'>
                <div class='field'>
                    <span class='label'>Name:</span> {$firstName} {$lastName}
                </div>
                <div class='field'>
                    <span class='label'>Email:</span> {$email}
                </div>
                <div class='field'>
                    <span class='label'>Phone:</span> " . ($phone ?: 'Not provided') . "
                </div>
                <div class='field'>
                    <span class='label'>Subject:</span> {$subject}
                </div>
                <div class='field'>
                    <span class='label'>Message:</span><br>
                    " . nl2br(htmlspecialchars($message)) . "
                </div>
                <div class='field'>
                    <span class='label'>Submitted:</span> " . date('Y-m-d H:i:s') . "
                </div>
            </div>
        </div>
    </body>
    </html>
    ";
    
    $headers = "From: " . SITE_NAME . " <" . SITE_EMAIL . ">\r\n";
    $headers .= "Reply-To: {$email}\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return sendEmail(ADMIN_EMAIL, $emailSubject, $emailBody, $headers);
}

/**
 * Get contact statistics (for admin dashboard)
 */
function getContactStats($conn) {
    $stats = [];
    
    // Total contacts
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM contacts");
    $stmt->execute();
    $stats['total'] = $stmt->fetchColumn();
    
    // New contacts (last 7 days)
    $stmt = $conn->prepare("SELECT COUNT(*) as recent FROM contacts WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $stmt->execute();
    $stats['recent'] = $stmt->fetchColumn();
    
    // Unread contacts
    $stmt = $conn->prepare("SELECT COUNT(*) as unread FROM contacts WHERE status = 'new'");
    $stmt->execute();
    $stats['unread'] = $stmt->fetchColumn();
    
    return $stats;
}

/**
 * Get recent contacts (for admin dashboard)
 */
function getRecentContacts($conn, $limit = 10) {
    $stmt = $conn->prepare("
        SELECT id, first_name, last_name, email, subject, created_at, status 
        FROM contacts 
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    $stmt->execute([$limit]);
    return $stmt->fetchAll();
}
?>
