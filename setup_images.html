<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Setup Guide - Sweet Delights</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #FFF5E1, #FFD1DC); }
        .card { border-radius: 15px; box-shadow: 0 5px 15px rgba(139, 69, 19, 0.1); }
        .image-placeholder { 
            background: linear-gradient(45deg, #8B4513, #FFD700); 
            color: white; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            min-height: 200px; 
            border-radius: 10px;
            margin-bottom: 10px;
        }
        .folder-structure { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 10px; 
            font-family: monospace; 
            white-space: pre-line;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header text-center bg-primary text-white">
                        <h2>🎂 Sweet Delights - Image Setup Guide</h2>
                        <p class="mb-0">Complete your website by adding beautiful baking images</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Introduction -->
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Important Note</h5>
                            <p>Your Sweet Delights website is ready to use! However, you'll need to add actual images to make it look professional. This guide will help you set up the required images.</p>
                        </div>

                        <!-- Required Folder Structure -->
                        <h4>📁 Required Folder Structure</h4>
                        <p>Create the following folders in your project directory and add images:</p>
                        <div class="folder-structure">images/
├── hero/
│   ├── hero1.jpg (1920x1080) - Main baking scene
│   ├── hero2.jpg (1920x1080) - Students learning
│   └── hero3.jpg (1920x1080) - Professional kitchen
├── gallery/
│   ├── student-cake1.jpg (800x600) - Student cake creation
│   ├── student-pastry1.jpg (800x600) - Student pastries
│   ├── student-bread1.jpg (800x600) - Student bread
│   ├── class-session1.jpg (800x600) - Class in session
│   ├── class-session2.jpg (800x600) - Pastry class
│   ├── class-session3.jpg (800x600) - Bread workshop
│   ├── graduation1.jpg (800x600) - Graduation ceremony
│   ├── competition1.jpg (800x600) - Baking competition
│   ├── workshop1.jpg (800x600) - Special workshop
│   ├── kitchen1.jpg (800x600) - Professional kitchen
│   ├── classroom1.jpg (800x600) - Theory classroom
│   └── equipment1.jpg (800x600) - Baking equipment
├── courses/
│   ├── basic-baking.jpg (600x400) - Basic baking course
│   ├── pastry-arts.jpg (600x400) - Pastry arts course
│   ├── artisan-bread.jpg (600x400) - Bread making course
│   ├── cake-decorating.jpg (600x400) - Cake decorating
│   ├── professional-diploma.jpg (600x400) - Professional course
│   └── specialty-workshops.jpg (600x400) - Workshop image
├── team/
│   ├── chef-maria.jpg (400x400) - Head chef photo
│   ├── chef-david.jpg (400x400) - Pastry chef photo
│   └── chef-sarah.jpg (400x400) - Bread expert photo
├── about/
│   └── our-story.jpg (800x600) - Institution story image
└── welcome-baking.jpg (800x600) - Welcome section image</div>

                        <!-- Image Recommendations -->
                        <h4 class="mt-4">📸 Image Recommendations</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Hero Images (1920x1080)</h6>
                                <ul>
                                    <li>Professional baking kitchen scenes</li>
                                    <li>Students actively learning</li>
                                    <li>Beautiful baked goods displays</li>
                                    <li>Warm, inviting atmosphere</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Gallery Images (800x600)</h6>
                                <ul>
                                    <li>Student work and creations</li>
                                    <li>Class sessions and workshops</li>
                                    <li>Facility and equipment photos</li>
                                    <li>Events and celebrations</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Free Image Sources -->
                        <h4 class="mt-4">🆓 Free Image Sources</h4>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Unsplash</h6>
                                        <p class="small">High-quality free photos</p>
                                        <a href="https://unsplash.com/s/photos/baking" target="_blank" class="btn btn-outline-primary btn-sm">Visit Unsplash</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Pexels</h6>
                                        <p class="small">Free stock photography</p>
                                        <a href="https://www.pexels.com/search/baking/" target="_blank" class="btn btn-outline-primary btn-sm">Visit Pexels</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Pixabay</h6>
                                        <p class="small">Free images and videos</p>
                                        <a href="https://pixabay.com/images/search/baking/" target="_blank" class="btn btn-outline-primary btn-sm">Visit Pixabay</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Keywords -->
                        <h4 class="mt-4">🔍 Recommended Search Keywords</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>For Hero Images:</h6>
                                <span class="badge bg-secondary me-1">professional kitchen</span>
                                <span class="badge bg-secondary me-1">baking class</span>
                                <span class="badge bg-secondary me-1">culinary school</span>
                                <span class="badge bg-secondary me-1">chef teaching</span>
                                <span class="badge bg-secondary me-1">baking workshop</span>
                            </div>
                            <div class="col-md-6">
                                <h6>For Gallery Images:</h6>
                                <span class="badge bg-info me-1">cake decorating</span>
                                <span class="badge bg-info me-1">bread making</span>
                                <span class="badge bg-info me-1">pastry chef</span>
                                <span class="badge bg-info me-1">baking equipment</span>
                                <span class="badge bg-info me-1">culinary students</span>
                            </div>
                        </div>

                        <!-- Quick Setup Steps -->
                        <h4 class="mt-4">⚡ Quick Setup Steps</h4>
                        <ol>
                            <li><strong>Create Folders:</strong> Create the folder structure shown above in your project directory</li>
                            <li><strong>Download Images:</strong> Use the free sources above to download appropriate images</li>
                            <li><strong>Resize Images:</strong> Use any image editor to resize images to the recommended dimensions</li>
                            <li><strong>Name Files:</strong> Rename files to match the exact names shown in the folder structure</li>
                            <li><strong>Test Website:</strong> Refresh your website to see the images in action</li>
                        </ol>

                        <!-- Placeholder Generator -->
                        <h4 class="mt-4">🎨 Temporary Placeholder Generator</h4>
                        <p>While you're gathering real images, you can use these placeholder services:</p>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>For Hero Images (1920x1080):</h6>
                                <code>https://picsum.photos/1920/1080</code>
                            </div>
                            <div class="col-md-6">
                                <h6>For Gallery Images (800x600):</h6>
                                <code>https://picsum.photos/800/600</code>
                            </div>
                        </div>

                        <!-- Tips -->
                        <div class="alert alert-success mt-4">
                            <h6><i class="fas fa-lightbulb me-2"></i>Pro Tips</h6>
                            <ul class="mb-0">
                                <li>Use consistent lighting and color tones across all images</li>
                                <li>Ensure images are high quality and properly compressed</li>
                                <li>Consider hiring a photographer for professional results</li>
                                <li>Always check image licenses before using</li>
                                <li>Optimize images for web (use tools like TinyPNG)</li>
                            </ul>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center mt-4">
                            <a href="index.html" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-home me-2"></i>View Website
                            </a>
                            <a href="admin/index.php" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-cog me-2"></i>Admin Panel
                            </a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
