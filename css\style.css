/* Sweet & Simple Theme - Baking Institution */

/* Color Variables */
:root {
    --cream-white: #FFF5E1;
    --chocolate-brown: #8B4513;
    --light-pink: #FFD1DC;
    --gold: #FFD700;
    --white: #FFFFFF;
    --dark-brown: #5D2F0A;
    --light-gray: #F8F9FA;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--chocolate-brown);
    background-color: var(--cream-white);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    color: var(--chocolate-brown);
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--chocolate-brown);
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--dark-brown);
    margin-bottom: 2rem;
}

/* Navigation */
.navbar {
    background-color: rgba(255, 245, 225, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--chocolate-brown) !important;
}

.navbar-brand i {
    color: var(--gold);
}

.nav-link {
    font-weight: 500;
    color: var(--chocolate-brown) !important;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: var(--gold) !important;
}

/* Hero Section */
.hero-section {
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-slide {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.7), rgba(255, 215, 0, 0.3));
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--cream-white);
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Buttons */
.btn-primary {
    background-color: var(--gold);
    border-color: var(--gold);
    color: var(--chocolate-brown);
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--chocolate-brown);
    border-color: var(--chocolate-brown);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 69, 19, 0.3);
}

.btn-outline-primary {
    border-color: var(--chocolate-brown);
    color: var(--chocolate-brown);
    font-weight: 500;
    padding: 10px 25px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--chocolate-brown);
    border-color: var(--chocolate-brown);
    color: var(--white);
}

.btn-outline-light {
    border-color: var(--white);
    color: var(--white);
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background-color: var(--white);
    color: var(--chocolate-brown);
}

/* Welcome Section */
.welcome-section {
    background-color: var(--white);
    padding: 80px 0;
}

.welcome-content h2 {
    color: var(--chocolate-brown);
    margin-bottom: 1rem;
}

.welcome-content .lead {
    color: var(--gold);
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.welcome-stats {
    margin-top: 2rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gold);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--chocolate-brown);
    font-weight: 500;
}

.welcome-image img {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(139, 69, 19, 0.2);
}

/* Services Preview */
.services-preview {
    background-color: var(--light-gray);
    padding: 80px 0;
}

.service-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--light-pink), var(--gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 2rem;
    color: var(--chocolate-brown);
}

.service-card h4 {
    color: var(--chocolate-brown);
    margin-bottom: 1rem;
}

.service-card p {
    color: var(--dark-brown);
    margin-bottom: 1.5rem;
}

/* Gallery Preview */
.gallery-preview {
    padding: 80px 0;
    background-color: var(--white);
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(139, 69, 19, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Footer */
.footer {
    background-color: var(--chocolate-brown);
    color: var(--cream-white);
    padding: 60px 0 20px;
}

.footer h5,
.footer h6 {
    color: var(--gold);
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--cream-white);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--gold);
}

.contact-info {
    list-style: none;
    padding: 0;
}

.contact-info li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.contact-info i {
    color: var(--gold);
    margin-right: 10px;
    width: 20px;
}

.social-links {
    margin-top: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--gold);
    color: var(--chocolate-brown);
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: var(--light-pink);
    transform: translateY(-3px);
}

.newsletter-form .form-control {
    border: none;
    border-radius: 25px 0 0 25px;
    padding: 10px 15px;
}

.newsletter-form .btn {
    border-radius: 0 25px 25px 0;
    padding: 10px 20px;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--chocolate-brown), var(--gold));
    padding: 120px 0 80px;
    margin-top: 76px;
}

.page-title {
    font-size: 3rem;
    color: var(--white);
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--cream-white);
    margin-bottom: 0;
}

/* About Page Styles */
.our-story {
    background-color: var(--white);
}

.story-content .lead {
    color: var(--gold);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

.highlight-item {
    text-align: center;
    padding: 1rem;
}

.highlight-item i {
    font-size: 2rem;
    color: var(--gold);
    margin-bottom: 1rem;
}

.highlight-item h5 {
    color: var(--chocolate-brown);
    margin-bottom: 0.5rem;
}

.highlight-item p {
    font-size: 0.9rem;
    color: var(--dark-brown);
}

.mission-card,
.vision-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    height: 100%;
    text-align: center;
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--light-pink), var(--gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.card-icon i {
    font-size: 2rem;
    color: var(--chocolate-brown);
}

.value-card {
    padding: 2rem 1rem;
    transition: all 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: var(--light-pink);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.value-icon i {
    font-size: 1.5rem;
    color: var(--chocolate-brown);
}

.team-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.team-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.team-info {
    padding: 1.5rem;
    text-align: center;
}

.team-role {
    color: var(--gold);
    font-weight: 500;
    margin-bottom: 1rem;
}

.team-social {
    margin-top: 1rem;
}

.team-social a {
    display: inline-block;
    width: 35px;
    height: 35px;
    background-color: var(--light-pink);
    color: var(--chocolate-brown);
    text-align: center;
    line-height: 35px;
    border-radius: 50%;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.team-social a:hover {
    background-color: var(--gold);
    transform: translateY(-2px);
}

/* Services Page Styles */
.course-categories {
    background-color: var(--white);
}

.category-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--light-pink), var(--gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.category-icon i {
    font-size: 2rem;
    color: var(--chocolate-brown);
}

.category-features {
    list-style: none;
    padding: 0;
    margin-top: 1.5rem;
}

.category-features li {
    padding: 0.5rem 0;
    color: var(--dark-brown);
    position: relative;
    padding-left: 1.5rem;
}

.category-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--gold);
    font-weight: bold;
}

.course-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.course-card.featured {
    border: 2px solid var(--gold);
}

.course-image {
    position: relative;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.course-level {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--chocolate-brown);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gold);
    color: var(--chocolate-brown);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.course-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.course-content h4 {
    color: var(--chocolate-brown);
    margin-bottom: 1rem;
}

.course-content p {
    color: var(--dark-brown);
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.course-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--dark-brown);
}

.detail-item i {
    color: var(--gold);
    margin-right: 10px;
    width: 16px;
}

.course-price {
    text-align: center;
    margin-bottom: 1.5rem;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--chocolate-brown);
}

.price-note {
    font-size: 0.9rem;
    color: var(--dark-brown);
    display: block;
}

.feature-item {
    padding: 2rem 1rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--light-pink);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--chocolate-brown);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    background: linear-gradient(45deg, var(--chocolate-brown), var(--gold));
    color: var(--white);
    border-radius: 15px 15px 0 0;
}

.modal-title {
    color: var(--white);
}

.btn-close {
    filter: invert(1);
}

.form-label {
    color: var(--chocolate-brown);
    font-weight: 500;
}

.form-control,
.form-select {
    border: 2px solid var(--cream-white);
    border-radius: 10px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--gold);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

/* Gallery Page Styles */
.gallery-filter {
    background-color: var(--light-gray);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-buttons .btn {
    margin: 5px;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-buttons .btn.active {
    background-color: var(--chocolate-brown);
    border-color: var(--chocolate-brown);
    color: var(--white);
}

.gallery-grid {
    background-color: var(--white);
}

.gallery-card {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.gallery-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-card:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.9), rgba(255, 215, 0, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-content {
    text-align: center;
    color: var(--white);
    padding: 1rem;
}

.gallery-content h5 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.gallery-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

/* Contact Page Styles */
.contact-info-section {
    background-color: var(--white);
}

.contact-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--light-pink), var(--gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.contact-icon i {
    font-size: 2rem;
    color: var(--chocolate-brown);
}

.contact-card h4 {
    color: var(--chocolate-brown);
    margin-bottom: 1rem;
}

.contact-card p {
    color: var(--dark-brown);
    margin-bottom: 1.5rem;
}

.contact-form-container {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
}

.map-container {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
}

.map-wrapper {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(139, 69, 19, 0.2);
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.info-item i {
    margin-right: 15px;
    margin-top: 5px;
    font-size: 1.2rem;
}

.info-content h6 {
    color: var(--chocolate-brown);
    margin-bottom: 0.5rem;
}

.info-content p {
    color: var(--dark-brown);
    font-size: 0.9rem;
    margin: 0;
}

.faq-section {
    background-color: var(--white);
}

.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(139, 69, 19, 0.1);
}

.accordion-button {
    background-color: var(--cream-white);
    color: var(--chocolate-brown);
    font-weight: 500;
    border: none;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--gold);
    color: var(--chocolate-brown);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    background-color: var(--white);
    color: var(--dark-brown);
    padding: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .welcome-stats .col-4 {
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .mission-card,
    .vision-card {
        padding: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .gallery-item img {
        height: 200px;
    }

    .team-image img {
        height: 200px;
    }
}
