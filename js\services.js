// Services page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    initEnrollmentModal();
    initCourseCards();
});

// Enrollment modal functionality
function initEnrollmentModal() {
    const enrollModal = document.getElementById('enrollModal');
    const enrollmentForm = document.getElementById('enrollmentForm');
    
    if (!enrollModal || !enrollmentForm) return;

    // Handle enrollment button clicks
    const enrollButtons = document.querySelectorAll('[data-bs-target="#enrollModal"]');
    
    enrollButtons.forEach(button => {
        button.addEventListener('click', function() {
            const courseName = this.getAttribute('data-course');
            const coursePrice = this.getAttribute('data-price');
            
            // Populate modal with course information
            populateEnrollmentModal(courseName, coursePrice);
        });
    });

    // Handle form submission
    enrollmentForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleEnrollmentSubmission(this);
    });

    // Set current date
    const enrollmentDateField = enrollmentForm.querySelector('input[name="enrollment_date"]');
    if (enrollmentDateField) {
        enrollmentDateField.value = new Date().toISOString().split('T')[0];
    }
}

// Populate enrollment modal with course data
function populateEnrollmentModal(courseName, coursePrice) {
    const selectedCourseField = document.getElementById('selectedCourse');
    const coursePriceField = document.getElementById('coursePrice');
    const modalTitle = document.getElementById('enrollModalLabel');
    
    if (selectedCourseField) {
        selectedCourseField.value = courseName;
    }
    
    if (coursePriceField) {
        coursePriceField.value = `$${coursePrice}`;
    }
    
    if (modalTitle) {
        modalTitle.textContent = `Enroll in ${courseName}`;
    }
}

// Handle enrollment form submission
function handleEnrollmentSubmission(form) {
    const formData = new FormData(form);
    const loader = window.SweetDelights.showLoading();
    
    // Validate form data
    if (!validateEnrollmentForm(formData)) {
        window.SweetDelights.hideLoading(loader);
        return;
    }
    
    // Simulate form submission (replace with actual AJAX call)
    setTimeout(() => {
        window.SweetDelights.hideLoading(loader);
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('enrollModal'));
        modal.hide();
        
        // Show success message
        window.SweetDelights.showNotification(
            'Enrollment request submitted successfully! We will contact you soon.',
            'success'
        );
        
        // Reset form
        form.reset();
        
        // Send confirmation email (simulate)
        sendEnrollmentConfirmation(formData);
        
    }, 2000);
}

// Validate enrollment form
function validateEnrollmentForm(formData) {
    const firstName = formData.get('first_name');
    const lastName = formData.get('last_name');
    const email = formData.get('email');
    const phone = formData.get('phone');
    const agreeTerms = formData.get('agree_terms');
    
    if (!firstName || firstName.trim().length < 2) {
        window.SweetDelights.showNotification('Please enter a valid first name.', 'error');
        return false;
    }
    
    if (!lastName || lastName.trim().length < 2) {
        window.SweetDelights.showNotification('Please enter a valid last name.', 'error');
        return false;
    }
    
    if (!email || !window.SweetDelights.validateEmail(email)) {
        window.SweetDelights.showNotification('Please enter a valid email address.', 'error');
        return false;
    }
    
    if (!phone || phone.trim().length < 10) {
        window.SweetDelights.showNotification('Please enter a valid phone number.', 'error');
        return false;
    }
    
    if (!agreeTerms) {
        window.SweetDelights.showNotification('Please agree to the terms and conditions.', 'error');
        return false;
    }
    
    return true;
}

// Send enrollment confirmation (simulate)
function sendEnrollmentConfirmation(formData) {
    const studentName = `${formData.get('first_name')} ${formData.get('last_name')}`;
    const courseName = formData.get('course_name');
    const email = formData.get('email');
    
    console.log('Enrollment Confirmation:', {
        student: studentName,
        course: courseName,
        email: email,
        timestamp: new Date().toISOString()
    });
    
    // In a real application, this would send an actual email
    setTimeout(() => {
        window.SweetDelights.showNotification(
            `Confirmation email sent to ${email}`,
            'info'
        );
    }, 3000);
}

// Course cards interactivity
function initCourseCards() {
    const courseCards = document.querySelectorAll('.course-card');
    
    courseCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
        
        // Add click to expand functionality
        const courseImage = card.querySelector('.course-image img');
        if (courseImage) {
            courseImage.addEventListener('click', function() {
                showCourseDetails(card);
            });
        }
    });
    
    // Category cards animation
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.2}s`;
        card.classList.add('fade-in-up');
    });
}

// Show course details in a modal or expanded view
function showCourseDetails(courseCard) {
    const courseName = courseCard.querySelector('h4').textContent;
    const courseDescription = courseCard.querySelector('p').textContent;
    const courseImage = courseCard.querySelector('img').src;
    const coursePrice = courseCard.querySelector('.price').textContent;
    
    // Create details modal
    const detailsModal = createCourseDetailsModal({
        name: courseName,
        description: courseDescription,
        image: courseImage,
        price: coursePrice
    });
    
    document.body.appendChild(detailsModal);
    
    // Show modal
    const modal = new bootstrap.Modal(detailsModal);
    modal.show();
    
    // Remove modal when hidden
    detailsModal.addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Create course details modal
function createCourseDetailsModal(course) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.setAttribute('tabindex', '-1');
    
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${course.name}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <img src="${course.image}" alt="${course.name}" class="img-fluid rounded mb-3">
                        </div>
                        <div class="col-md-6">
                            <h6>Course Description</h6>
                            <p>${course.description}</p>
                            <h6>Price</h6>
                            <p class="h4 text-primary">${course.price}</p>
                            <h6>What You'll Learn</h6>
                            <ul>
                                <li>Professional baking techniques</li>
                                <li>Recipe development and scaling</li>
                                <li>Food safety and hygiene</li>
                                <li>Equipment operation and maintenance</li>
                                <li>Presentation and plating skills</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="enrollInCourse('${course.name}', '${course.price.replace('$', '')}')">
                        Enroll Now
                    </button>
                </div>
            </div>
        </div>
    `;
    
    return modal;
}

// Enroll in course function (called from details modal)
function enrollInCourse(courseName, coursePrice) {
    // Close details modal
    const detailsModal = document.querySelector('.modal.show');
    if (detailsModal) {
        const modal = bootstrap.Modal.getInstance(detailsModal);
        modal.hide();
    }
    
    // Open enrollment modal
    setTimeout(() => {
        populateEnrollmentModal(courseName, coursePrice);
        const enrollModal = new bootstrap.Modal(document.getElementById('enrollModal'));
        enrollModal.show();
    }, 300);
}

// Course comparison functionality
function initCourseComparison() {
    const compareButtons = document.querySelectorAll('.compare-course');
    const comparisonList = [];
    
    compareButtons.forEach(button => {
        button.addEventListener('click', function() {
            const courseCard = this.closest('.course-card');
            const courseName = courseCard.querySelector('h4').textContent;
            
            if (comparisonList.includes(courseName)) {
                // Remove from comparison
                const index = comparisonList.indexOf(courseName);
                comparisonList.splice(index, 1);
                this.classList.remove('active');
                this.textContent = 'Compare';
            } else if (comparisonList.length < 3) {
                // Add to comparison
                comparisonList.push(courseName);
                this.classList.add('active');
                this.textContent = 'Remove';
            } else {
                window.SweetDelights.showNotification('You can compare up to 3 courses at a time.', 'info');
            }
            
            updateComparisonUI();
        });
    });
}

// Update comparison UI
function updateComparisonUI() {
    let comparisonBar = document.querySelector('.comparison-bar');
    
    if (comparisonList.length > 0) {
        if (!comparisonBar) {
            comparisonBar = createComparisonBar();
            document.body.appendChild(comparisonBar);
        }
        
        comparisonBar.querySelector('.comparison-count').textContent = comparisonList.length;
        comparisonBar.style.display = 'block';
    } else if (comparisonBar) {
        comparisonBar.style.display = 'none';
    }
}

// Create comparison bar
function createComparisonBar() {
    const bar = document.createElement('div');
    bar.className = 'comparison-bar';
    bar.style.cssText = `
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(45deg, #8B4513, #FFD700);
        color: white;
        padding: 15px;
        text-align: center;
        z-index: 1000;
        display: none;
    `;
    
    bar.innerHTML = `
        <span class="comparison-count">0</span> courses selected for comparison
        <button class="btn btn-light btn-sm ms-3" onclick="showComparison()">Compare Now</button>
        <button class="btn btn-outline-light btn-sm ms-2" onclick="clearComparison()">Clear</button>
    `;
    
    return bar;
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    .fade-in-up {
        animation: fadeInUp 0.6s ease forwards;
        opacity: 0;
        transform: translateY(30px);
    }
    
    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .course-card {
        transition: all 0.3s ease;
    }
    
    .course-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
    }
`;
document.head.appendChild(style);
