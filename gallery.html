<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery - Sweet Delights Baking Institution</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="images/logo.png" alt="Lakshmi Sai Baking Classes Logo" class="logo-img">
                <div class="brand-text">
                    <span class="main-text">Lakshmi Sai</span>
                    <span class="sub-text">Baking Classes</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.html">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="gallery.html">Gallery</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="page-title">Our Gallery</h1>
                    <p class="page-subtitle">Discover the amazing creations from our students and classes</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Filter -->
    <section class="gallery-filter py-4 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="filter-buttons text-center">
                        <button class="btn btn-outline-primary active" data-filter="all">All</button>
                        <button class="btn btn-outline-primary" data-filter="student-work">Student Work</button>
                        <button class="btn btn-outline-primary" data-filter="classes">Classes</button>
                        <button class="btn btn-outline-primary" data-filter="events">Events</button>
                        <button class="btn btn-outline-primary" data-filter="facilities">Facilities</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Grid -->
    <section class="gallery-grid py-5">
        <div class="container">
            <div class="row" id="galleryContainer">
                <!-- Student Work -->
                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="student-work">
                    <div class="gallery-card">
                        <img src="images/gallery/student-cake1.jpg" alt="Student Cake Creation" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Chocolate Layer Cake</h5>
                                <p>Created by Sarah M. - Basic Baking Course</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/student-cake1.jpg" 
                                        data-title="Chocolate Layer Cake" 
                                        data-description="A beautiful three-layer chocolate cake with buttercream frosting, created by Sarah during her Basic Baking Course. This was her final project showcasing fundamental cake-making techniques.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="student-work">
                    <div class="gallery-card">
                        <img src="images/gallery/student-pastry1.jpg" alt="Student Pastry Creation" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>French Macarons</h5>
                                <p>Created by Mike T. - Pastry Arts Course</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/student-pastry1.jpg" 
                                        data-title="French Macarons" 
                                        data-description="Perfectly crafted French macarons in various flavors by Mike from our Pastry Arts Course. These delicate treats showcase advanced piping and flavor techniques.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="student-work">
                    <div class="gallery-card">
                        <img src="images/gallery/student-bread1.jpg" alt="Student Bread Creation" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Artisan Sourdough</h5>
                                <p>Created by Emma L. - Bread Making Course</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/student-bread1.jpg" 
                                        data-title="Artisan Sourdough" 
                                        data-description="Beautiful artisan sourdough loaves with perfect crust and crumb structure, baked by Emma in our Artisan Bread Making course.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Classes -->
                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="classes">
                    <div class="gallery-card">
                        <img src="images/gallery/class-session1.jpg" alt="Baking Class Session" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Hands-on Learning</h5>
                                <p>Students practicing cake decorating techniques</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/class-session1.jpg" 
                                        data-title="Hands-on Learning" 
                                        data-description="Our students actively learning cake decorating techniques in our professional kitchen environment with expert guidance from our instructors.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="classes">
                    <div class="gallery-card">
                        <img src="images/gallery/class-session2.jpg" alt="Pastry Class" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Pastry Techniques</h5>
                                <p>Learning advanced pastry folding methods</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/class-session2.jpg" 
                                        data-title="Pastry Techniques" 
                                        data-description="Students mastering the art of laminated dough and pastry folding techniques under the guidance of our expert pastry chef.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="classes">
                    <div class="gallery-card">
                        <img src="images/gallery/class-session3.jpg" alt="Bread Making Class" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Bread Workshop</h5>
                                <p>Students learning traditional kneading techniques</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/class-session3.jpg" 
                                        data-title="Bread Workshop" 
                                        data-description="A hands-on bread making workshop where students learn traditional kneading and shaping techniques for artisan breads.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Events -->
                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="events">
                    <div class="gallery-card">
                        <img src="images/gallery/graduation1.jpg" alt="Graduation Ceremony" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Graduation Day</h5>
                                <p>Celebrating our successful graduates</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/graduation1.jpg" 
                                        data-title="Graduation Day" 
                                        data-description="A proud moment as our students receive their certificates after completing their baking courses and demonstrating their newly acquired skills.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="events">
                    <div class="gallery-card">
                        <img src="images/gallery/competition1.jpg" alt="Baking Competition" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Annual Baking Contest</h5>
                                <p>Students showcasing their creativity</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/competition1.jpg" 
                                        data-title="Annual Baking Contest" 
                                        data-description="Our annual baking competition where students showcase their creativity and skills in a friendly competitive environment.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="events">
                    <div class="gallery-card">
                        <img src="images/gallery/workshop1.jpg" alt="Special Workshop" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Holiday Workshop</h5>
                                <p>Special seasonal baking techniques</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/workshop1.jpg" 
                                        data-title="Holiday Workshop" 
                                        data-description="A special holiday workshop where students learn to create festive treats and seasonal decorations for the holiday season.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Facilities -->
                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="facilities">
                    <div class="gallery-card">
                        <img src="images/gallery/kitchen1.jpg" alt="Professional Kitchen" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Professional Kitchen</h5>
                                <p>State-of-the-art baking equipment</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/kitchen1.jpg" 
                                        data-title="Professional Kitchen" 
                                        data-description="Our state-of-the-art professional kitchen equipped with commercial-grade ovens, mixers, and all the tools needed for professional baking education.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="facilities">
                    <div class="gallery-card">
                        <img src="images/gallery/classroom1.jpg" alt="Theory Classroom" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Theory Classroom</h5>
                                <p>Modern learning environment</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/classroom1.jpg" 
                                        data-title="Theory Classroom" 
                                        data-description="Our modern classroom where students learn the theory behind baking science, nutrition, and business aspects of the baking industry.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 mb-4 gallery-item" data-category="facilities">
                    <div class="gallery-card">
                        <img src="images/gallery/equipment1.jpg" alt="Baking Equipment" class="img-fluid">
                        <div class="gallery-overlay">
                            <div class="gallery-content">
                                <h5>Professional Equipment</h5>
                                <p>Industry-standard tools and machines</p>
                                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#galleryModal" 
                                        data-image="images/gallery/equipment1.jpg" 
                                        data-title="Professional Equipment" 
                                        data-description="Industry-standard baking equipment including spiral mixers, proof cabinets, and specialized tools that students will encounter in professional kitchens.">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Modal -->
    <div class="modal fade" id="galleryModal" tabindex="-1" aria-labelledby="galleryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="galleryModalLabel">Gallery Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="" class="img-fluid rounded mb-3">
                    <h5 id="modalTitle"></h5>
                    <p id="modalDescription"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-logo">
                        <img src="images/logo.png" alt="Lakshmi Sai Baking Classes Logo" class="logo-img">
                        <div class="brand-text">
                            <span class="main-text">Lakshmi Sai</span>
                            <span class="sub-text">Baking Classes</span>
                        </div>
                    </div>
                    <p>Training at the best! Learn professional baking techniques with hands-on experience and expert guidance.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Quick Links</h6>
                    <ul class="footer-links">
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6>Contact Info</h6>
                    <ul class="contact-info">
                        <li><i class="fas fa-map-marker-alt"></i> Sri Vyshnavi Indralok Phase II, Flat 101, BWSSB Road, Challaghatta, Bangalore - 560017</li>
                        <li><i class="fas fa-phone"></i> 9916192449</li>
                        <li><i class="fas fa-user"></i> Contact: Indu</li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6>Newsletter</h6>
                    <p>Subscribe to get updates on new courses and events.</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Your email">
                            <button class="btn btn-primary" type="submit">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2024 Lakshmi Sai Baking Classes. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>
    <script src="js/gallery.js"></script>
</body>
</html>
